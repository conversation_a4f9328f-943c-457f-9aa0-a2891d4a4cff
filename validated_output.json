{"_id": {"$oid": "67c037e1f5ef507d5e4855a4"}, "id": "107fb583-47e7-4827-ba51-df9531ff0228", "app_id": "3e55dbba-fa8b-4783-a48a-bbc5e327e50b", "aria_status": "6b510955-1467-4ca6-bd87-1eb8f1b3eee9", "aria_assigned_to": "e98a322a-1b23-4b7c-8d59-d578c09f9d6d", "aria_created": {"$date": "2025-02-27T10:01:05.292Z"}, "aria_last_update": {"$date": "2025-07-21T11:45:36.117Z"}, "aria_exception": "", "completed_ocr_job": true, "ongoing_initial_bre": false, "groups": {"bill_of_sale": {"ocr_data": {"link": "", "ocr_status": 1, "error": "", "s3_location": "", "extracted_at": {"$date": "2025-07-22T10:20:05.603Z"}}, "bre_exceptions": {"vin": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "year": "Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "make": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "model": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "odometer_reading": "Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "buyer_name": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "buyer_address": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "sale_price": "Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "tavt_tax_amount": "Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "total_amount_due": "Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "lien_holder_name": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "dealer_fees": "Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']"}, "fields": {"deal_number": {"value": "320629", "confidence": 0, "coordinates": {}, "pageno": null}, "stock_number": {"value": "D5823P", "confidence": 99.84375, "coordinates": {"normalized": {"left": 0.35661670565605164, "top": 0.06138310581445694, "width": 0.045158740133047104, "height": 0.012023304589092731}}, "pageno": 1}, "vin": {"value": "SALWR2SU2MA776022", "confidence": 98.25016, "coordinates": {"normalized": {"left": 0.5082540512084961, "top": 0.21827977895736694, "width": 0.12770585715770721, "height": 0.012053772807121277}}, "pageno": 1}, "year": {"value": "2021", "confidence": 99.92188, "coordinates": {"normalized": {"left": 0.0534219890832901, "top": 0.21804891526699066, "width": 0.029197053983807564, "height": 0.012257514521479607}}, "pageno": 1}, "make": {"value": "LAND ROVER", "confidence": 99.92676, "coordinates": {"normalized": {"left": 0.1261538565158844, "top": 0.04628590866923332, "width": 0.09904790297150612, "height": 0.015583878383040428}}, "pageno": 1}, "model": {"value": "RANGE ROVER SPO", "confidence": 99.91536, "coordinates": {"normalized": {"left": 0.23501534759998322, "top": 0.21881073713302612, "width": 0.11389682441949844, "height": 0.011476299725472927}}, "pageno": 1}, "odometer_reading": {"value": "42806", "confidence": 99.95117, "coordinates": {"normalized": {"left": 0.6832162737846375, "top": 0.2572130858898163, "width": 0.037191085517406464, "height": 0.012363378889858723}}, "pageno": 1}, "buyer_name": {"value": "MEWAEL H HABTEGABIR", "confidence": 99.4785, "coordinates": {"normalized": {"left": 0.05275219306349754, "top": 0.10079672187566757, "width": 0.1451273448765278, "height": 0.011724651791155338}}, "pageno": 1}, "co_buyer_name": {"value": "N/A", "confidence": 99.88281, "coordinates": {"normalized": {"left": 0.****************, "top": 0.****************, "width": 0.023084493353962898, "height": 0.013856234028935432}}, "pageno": 1}, "buyer_address": {"value": "911 WINDING DOWN WAY, GRAYSON GA 30017", "confidence": 0, "coordinates": {}, "pageno": null}, "sale_price": {"value": "48900.00", "confidence": 99.81445, "coordinates": {"normalized": {"left": 0.9029613733291626, "top": 0.09536323696374893, "width": 0.059924595057964325, "height": 0.012531159445643425}}, "pageno": 1}, "tavt_tax_amount": {"value": "3426.29", "confidence": 99.84375, "coordinates": {"normalized": {"left": 0.9106782078742981, "top": 0.5222641229629517, "width": 0.05211430788040161, "height": 0.012497365474700928}}, "pageno": 1}, "trade_in_value": {"value": "N/A", "confidence": 99.88281, "coordinates": {"normalized": {"left": 0.****************, "top": 0.****************, "width": 0.023084493353962898, "height": 0.013856234028935432}}, "pageno": 1}, "total_amount_due": {"value": "52416.29", "confidence": 99.89258, "coordinates": {"normalized": {"left": 0.****************, "top": 0.****************, "width": 0.*****************, "height": 0.012891356833279133}}, "pageno": 1}, "lien_holder_name": {"value": "JPMORGAN CHASE BANK NA", "confidence": 99.92188, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.*****************, "width": 0.*****************, "height": 0.011326683685183525}}, "pageno": 1}, "dealer_fees": {"value": "998.00", "confidence": 99.85352, "coordinates": {"normalized": {"left": 0.****************, "top": 0.****************, "width": 0.****************, "height": 0.012547466903924942}}, "pageno": 1}}, "display": true}, "driver_license": {"ocr_data": {"link": "", "ocr_status": 1, "error": "", "s3_location": "", "extracted_at": {"$date": "2025-07-22T06:03:31.291Z"}}, "bre_exceptions": {"date_of_birth": "Unsupported validation type: DATE. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "expiration_date": "Unsupported validation type: DATE. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']"}, "fields": {"full_name": {"value": "TARRA ANTOINETTE HOLMAN", "confidence": 0, "coordinates": {}, "pageno": null}, "date_of_birth": {"value": "08/04/1980", "confidence": 100, "coordinates": {"normalized": {"left": 0.585292398929596, "top": 0.07798399776220322, "width": 0.12569332122802734, "height": 0.015999438241124153}}, "pageno": 1}, "address": {"value": "2793 LAKEVIEW WALK", "confidence": 99.98372, "coordinates": {"normalized": {"left": 0.2983531653881073, "top": 0.17942799627780914, "width": 0.22387000918388367, "height": 0.013785304501652718}}, "pageno": 1}, "city": {"value": "DECATUR", "confidence": 0, "coordinates": {}, "pageno": null}, "state": {"value": "GA", "confidence": 99.95117, "coordinates": {"normalized": {"left": 0.7376906275749207, "top": 0.0353194959461689, "width": 0.02404826320707798, "height": 0.009680344723165035}}, "pageno": 1}, "zip": {"value": "30035-4061", "confidence": 99.96094, "coordinates": {"normalized": {"left": 0.44472452998161316, "top": 0.19571664929389954, "width": 0.10552851855754852, "height": 0.013196797110140324}}, "pageno": 1}, "driver's_license_number": {"value": "057014755", "confidence": 99.9707, "coordinates": {"normalized": {"left": 0.36484596133232117, "top": 0.07560957223176956, "width": 0.13082975149154663, "height": 0.017121797427535057}}, "pageno": 1}, "expiration_date": {"value": "08/04/2025", "confidence": 100, "coordinates": {"normalized": {"left": 0.5850598216056824, "top": 0.09861645102500916, "width": 0.11882589757442474, "height": 0.015350242145359516}}, "pageno": 1}}, "display": true}, "title": {"ocr_data": {"link": "", "ocr_status": 1, "error": "", "s3_location": "", "extracted_at": {"$date": "2025-07-22T10:06:39.487Z"}}, "bre_exceptions": {"vin": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "year": "Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "make": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "model": "Field is required; Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)", "odometer_reading": "Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "selling_dealer_name": "Field is required; Selling dealer name is required", "lien_holder_name": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "date_of_transfer": "Unsupported validation type: DATE. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "buyer_name": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "seller_signature": "Unsupported validation type: BOOLEAN. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: BOOLEAN. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']"}, "fields": {"vin": {"value": "SALWR2SU2MA776022", "confidence": 89.16833, "coordinates": {"normalized": {"left": 0.15952757000923157, "top": 0.24352653324604034, "width": 0.19939130544662476, "height": 0.011764195747673512}}, "pageno": 1}, "year": {"value": "2021", "confidence": 99.85352, "coordinates": {"normalized": {"left": 0.535572350025177, "top": 0.243750661611557, "width": 0.0449182502925396, "height": 0.011528280563652515}}, "pageno": 1}, "make": {"value": "LNDR", "confidence": 99.60938, "coordinates": {"normalized": {"left": 0.5943421125411987, "top": 0.24402309954166412, "width": 0.04596005380153656, "height": 0.011199094355106354}}, "pageno": 1}, "model": {"value": "N/A", "confidence": 0, "coordinates": {}, "pageno": null}, "body_style": {"value": "UT", "confidence": 99.81445, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.*****************, "width": 0.021464714780449867, "height": 0.011165722273290157}}, "pageno": 1}, "odometer_reading": {"value": "42797", "confidence": 0, "coordinates": {}, "pageno": null}, "selling_dealer_name": {"value": "N/A", "confidence": 0, "coordinates": {}, "pageno": null}, "lien_holder_name": {"value": "JPMORGAN CHASE BANK NA", "confidence": 99.82626, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.****************, "width": 0.****************, "height": 0.011071995832026005}}, "pageno": 1}, "lien_satisfied": {"value": "N/A", "confidence": 0, "coordinates": {}, "pageno": null}, "date_of_transfer": {"value": "02/02/2022", "confidence": 99.88201, "coordinates": {"normalized": {"left": 0.****************, "top": 0.****************, "width": 0.*****************, "height": 0.011617189273238182}}, "pageno": 1}, "buyer_name": {"value": "<PERSON>", "confidence": 0, "coordinates": {}, "pageno": null}, "title_number": {"value": "V75210219J9", "confidence": 98.20213, "coordinates": {"normalized": {"left": 0.****************, "top": 0.*****************, "width": 0.*****************, "height": 0.011656071990728378}}, "pageno": 1}, "seller_signature": {"value": "<PERSON>", "confidence": 62.46034, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.****************, "width": 0.04738588631153107, "height": 0.0264377873390913}}, "pageno": 1}}, "display": true}, "red_reassignment": {"ocr_data": {"link": "", "ocr_status": 1, "error": "", "s3_location": "", "extracted_at": {"$date": "2025-07-22T09:49:27.423Z"}}, "bre_exceptions": {"vin": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "year": "Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "make": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "model": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "odometer_reading": "Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "odometer_type": "Field is required; Odometer type is required", "buyer_name": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "buyer_address": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "date_of_reassignment": "Unsupported validation type: DATE. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: DATE. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']"}, "fields": {"vin": {"value": "SALWR2SU2MA776022", "confidence": 84.96831, "coordinates": {"normalized": {"left": 0.27728137373924255, "top": 0.3271561861038208, "width": 0.6648351550102234, "height": 0.010017559863626957}}, "pageno": 1}, "year": {"value": "2021", "confidence": 99.9707, "coordinates": {"normalized": {"left": 0.08882250636816025, "top": 0.3576160967350006, "width": 0.03753316029906273, "height": 0.009244551882147789}}, "pageno": 1}, "make": {"value": "LAND ROVER", "confidence": 99.99512, "coordinates": {"normalized": {"left": 0.23548634350299835, "top": 0.2103981077671051, "width": 0.09819573536515236, "height": 0.008854854851961136}}, "pageno": 1}, "model": {"value": "RANGE ROVER SPO", "confidence": 99.92839, "coordinates": {"normalized": {"left": 0.5096811652183533, "top": 0.3581100404262543, "width": 0.14697307720780373, "height": 0.008778720162808895}}, "pageno": 1}, "odometer_reading": {"value": "42806", "confidence": 99.11491, "coordinates": {"normalized": {"left": 0.4517662525177002, "top": 0.39548543095588684, "width": 0.14554214477539062, "height": 0.009339001029729843}}, "pageno": 1}, "odometer_type": {"value": "N/A", "confidence": 99.99023, "coordinates": {"normalized": {"left": 0.1859833300113678, "top": 0.5805671811103821, "width": 0.030441762879490852, "height": 0.01090993918478489}}, "pageno": 1}, "buyer_name": {"value": "MEWAEL H HABTEGABIR", "confidence": 99.87471, "coordinates": {"normalized": {"left": 0.1660560816526413, "top": 0.4831177890300751, "width": 0.18734080344438553, "height": 0.008779074996709824}}, "pageno": 1}, "buyer_address": {"value": "911 WINDING DOWN WAY, GRAYSON GA 30017", "confidence": 0, "coordinates": {}, "pageno": null}, "date_of_reassignment": {"value": "10/14/24", "confidence": 69.42981, "coordinates": {"normalized": {"left": 0.8643506169319153, "top": 0.4691908061504364, "width": 0.07501699775457382, "height": 0.015443132258951664}}, "pageno": 2}, "certification": {"value": "The undersigned hereby certifies that the information contained herein is true and accurate. Pursuant to O.C.G.A. Ã‚Â§ 40-3-1 et seq. & 16-10-71, a person to whom a lawful oath or affirmation has been administered or who executes a document knowing that it purports to be an acknowledgment of a lawful oath or affirmation commits the offense of false swearing when, in any matter or thing other than a judicial proceeding, he knowingly and willfully makes a false statement. A person convicted of the offense of false swearing shall be punished by a fine of not more than $1,000.00 or by imprisonment for not less than one nor more than five years, or both. A dealer of new or used motor vehicles that accepts an application for title and state and local title ad valorem tax fees and converts such fees to his or her own use shall be guilty of theft by conversion and, upon conviction, shall be punished as provided O.C.G.A. 16-8-12 and O.C.G.A. 48-5C-1(b)(1)(F).", "confidence": 0, "coordinates": {}, "pageno": null}}, "display": true}, "title_application": {"ocr_data": {"link": "", "ocr_status": 1, "error": "", "s3_location": "", "extracted_at": {"$date": "2025-07-22T09:42:36.217Z"}}, "bre_exceptions": {"buyer_address": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "vin": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "year": "Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "make": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "model": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "odometer_reading": "Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: CROSS_FIELD. Supported types: ['REG<PERSON>', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "lien_holder_name": "Unsupported validation type: CROSS_FIELD. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']", "sale_price": "Field is required; Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']; Unsupported validation type: NUMERIC. Supported types: ['REGEX', 'REGEX_LIST', 'EXPRESSION_TYPE', 'EXPRESSION_TYPE_LIST']"}, "fields": {"buyer_full_name": {"value": "MEWAEL H HABTEGABIR", "confidence": 99.82668, "coordinates": {"normalized": {"left": 0.15663233399391174, "top": 0.41898754239082336, "width": 0.18764110654592514, "height": 0.009174241684377193}}, "pageno": 1}, "co_buyer_name": {"value": "N/A", "confidence": 0, "coordinates": {}, "pageno": null}, "buyer_address": {"value": "911 WINDING DOWN WAY GRAYSON GA 30017", "confidence": 99.92048, "coordinates": {"normalized": {"left": 0.10916532576084137, "top": 0.4603900909423828, "width": 0.37124934047460556, "height": 0.009633663110435009}}, "pageno": 1}, "city": {"value": "GRAYSON", "confidence": 99.93164, "coordinates": {"normalized": {"left": 0.3239567279815674, "top": 0.4609775245189667, "width": 0.06891235709190369, "height": 0.008559583686292171}}, "pageno": 1}, "state": {"value": "GA", "confidence": 99.79492, "coordinates": {"normalized": {"left": 0.4228355586528778, "top": 0.17327862977981567, "width": 0.02209128998219967, "height": 0.009919081814587116}}, "pageno": 1}, "zip": {"value": "30017", "confidence": 99.93164, "coordinates": {"normalized": {"left": 0.4326685070991516, "top": 0.4603900909423828, "width": 0.04774615913629532, "height": 0.009633663110435009}}, "pageno": 1}, "county_of_residence": {"value": "GWINNETT", "confidence": 99.93164, "coordinates": {"normalized": {"left": 0.6183019280433655, "top": 0.1727200299501419, "width": 0.07865459471940994, "height": 0.009007895365357399}}, "pageno": 1}, "customer_id": {"value": "055765625", "confidence": 99.92188, "coordinates": {"normalized": {"left": 0.697380542755127, "top": 0.4184077978134155, "width": 0.0871170163154602, "height": 0.009993732906877995}}, "pageno": 1}, "vin": {"value": "SALWR2SU2MA776022", "confidence": 98.75937, "coordinates": {"normalized": {"left": 0.15762971341609955, "top": 0.14564092457294464, "width": 0.16573864221572876, "height": 0.009114515967667103}}, "pageno": 1}, "year": {"value": "2021", "confidence": 99.96094, "coordinates": {"normalized": {"left": 0.8738778233528137, "top": 0.14544391632080078, "width": 0.036437466740608215, "height": 0.009793137200176716}}, "pageno": 1}, "make": {"value": "LAND ROVER", "confidence": 99.93164, "coordinates": {"normalized": {"left": 0.15748704969882965, "top": 0.15745072066783905, "width": 0.09833421930670738, "height": 0.009099181741476059}}, "pageno": 1}, "model": {"value": "RANGE ROVER SPO", "confidence": 99.96745, "coordinates": {"normalized": {"left": 0.1572292149066925, "top": 0.1726674884557724, "width": 0.14744254387915134, "height": 0.009047387167811394}}, "pageno": 1}, "body_style": {"value": "TURBO I6 MHEV HSE SI", "confidence": 99.77188, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.****************, "width": 0.*****************, "height": 0.009458083659410477}}, "pageno": 1}, "odometer_reading": {"value": "42806", "confidence": 100, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.*****************, "width": 0.*****************, "height": 0.009720140136778355}}, "pageno": 1}, "lien_holder_name": {"value": "JPMORGAN CHASE BANK NA", "confidence": 99.94853, "coordinates": {"normalized": {"left": 0.****************, "top": 0.****************, "width": 0.*****************, "height": 0.009222496300935745}}, "pageno": 1}, "lien_holder_address": {"value": "700 KANSAS LANE, LA4-4041 MONROE LA 71203", "confidence": 99.65752, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.****************, "width": 0.***************, "height": 0.010850606486201286}}, "pageno": 1}, "dealer_name": {"value": "JAGUAR LAND ROVER GWINNETT", "confidence": 99.96054, "coordinates": {"normalized": {"left": 0.*****************, "top": 0.****************, "width": 0.*****************, "height": 0.008843421004712582}}, "pageno": 1}, "dealer_number": {"value": "************", "confidence": 96.62647, "coordinates": {"normalized": {"left": 0.040406305342912674, "top": 0.****************, "width": 0.*****************, "height": 0.*****************}}, "pageno": 1}, "sale_price": {"value": "N/A", "confidence": 0, "coordinates": {}, "pageno": null}}, "display": true}}, "ocr_groups": ["bill_of_sale", "driver_license", "title_application", "red_reassignment", "title"], "aria_user": "<PERSON><PERSON>", "blocked": false, "blocked_at": null, "on_going_initial_bre": true, "flag": null}