{"_id": {"$oid": "687e2a5b0d1d5c7eaa91ffc9"}, "validation_type_key": "tag_titles", "properties": {"fields": {"deal_number": {"required": true, "type": "string", "validation_rules": [{"check": "deal_number_validation", "isValidationType": "REGEX_LIST", "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"], "error_msgs": ["Deal number is required", "Deal number should be alphanumeric"], "conditionType": "AND"}]}, "stock_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9-]{1,20}$", "error_msg": "Stock number is required and should be alphanumeric with hyphens (1-20 characters)"}]}, "vin": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN is required"}, {"check": "vin_format", "isValidationType": "REGEX", "regex": "^[A-HJ-NPR-Z0-9]{17}$", "error_msg": "VIN should be exactly 17 characters (excluding I, O, Q)"}, {"check": "vin_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.vin.value == mv1.vin.value && mv1.vin.value == mv7d.vin.value && mv7d.vin.value == title.vin.value", "error_msg": "VIN number does not match across documents"}]}, "year": {"required": true, "type": "integer", "validation_rules": [{"check": "valid_year", "isValidationType": "REGEX", "regex": "^(19[0-9]{2}|20[0-2][0-9]|2030)$", "error_msg": "Year is required and should be between 1900 and 2030"}, {"check": "year_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.year.value == mv1.year.value && mv1.year.value == mv7d.year.value && mv7d.year.value == title.year.value", "error_msg": "Year does not match across documents"}]}, "make": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s-]{1,30}$", "error_msg": "Make is required and should contain only letters, spaces, and hyphens (1-30 characters)"}, {"check": "make_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.make.value == mv1.make.value && mv1.make.value == mv7d.make.value && mv7d.make.value == title.make.value", "error_msg": "Make does not match across documents"}]}, "model": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,30}$", "error_msg": "Model is required and should contain only letters, numbers, spaces, and hyphens (1-30 characters)"}, {"check": "model_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.model.value == mv1.model.value && mv1.model.value == mv7d.model.value && mv7d.model.value == title.model.value", "error_msg": "Model does not match across documents"}]}, "odometer_reading": {"required": true, "type": "integer", "validation_rules": [{"check": "odometer_range", "isValidationType": "REGEX", "regex": "^(0|[1-9][0-9]{0,5})$", "error_msg": "Odometer reading should be a positive number between 0 and 999,999"}, {"check": "odometer_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "abs(bos.odometer_reading.value - mv1.odometer_reading.value) <= 10 && abs(mv1.odometer_reading.value - mv7d.odometer_reading.value) <= 10 && abs(mv7d.odometer_reading.value - title.odometer_reading.value) <= 10", "error_msg": "Odometer reading does not match across documents"}, {"check": "odometer_title_exception", "isValidationType": "EXPRESSION_TYPE", "expression": "title.odometer_reading.value <= bos.odometer_reading.value", "error_msg": "Title odometer may be less than or equal to BOS odometer reading - this is acceptable"}]}, "buyer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Buyer name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}, {"check": "buyer_name_consistency", "isValidationType": "EXPRESSION_TYPE_LIST", "expressions": ["bos.buyer_name.value != null && bos.buyer_name.value != \"\"", "dl.full_name.value != null && dl.full_name.value != \"\"", "mv1.buyer_full_name.value != null && mv1.buyer_full_name.value != \"\"", "bos.buyer_name.value == dl.full_name.value && dl.full_name.value == mv1.buyer_full_name.value && mv1.buyer_full_name.value == mv7d.buyer_name.value && mv7d.buyer_name.value == title.buyer_name.value"], "error_msgs": ["BOS buyer name is required", "DL full name is required", "MV1 buyer full name is required", "Buyer name does not match across documents"], "conditionType": "AND"}]}, "co_buyer_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Co-buyer name should contain only letters, spaces, hyphens and apostrophes"}, {"check": "co_buyer_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "bos.co_buyer_name.value == mv1.co_buyer_name.value && mv1.co_buyer_name.value == mv7d.co_buyer_name.value", "error_msg": "Co-buyer name does not match across documents"}]}, "buyer_address": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer address is required"}, {"check": "address_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "true", "error_msg": "Buyer address consistency check disabled"}, {"check": "lien_address_consistency", "isValidationType": "EXPRESSION_TYPE", "expression": "buyer_address.value == mv1.lien_holder_address.value", "error_msg": "Buyer address does not match lien holder address in MV1"}]}, "sale_price": {"required": true, "type": "decimal", "validation_rules": [{"check": "sale_price_range", "isValidationType": "REGEX", "regex": "^(0\\.(0[1-9]|[1-9][0-9]?)|[1-9][0-9]{0,5}(\\.[0-9]{1,2})?|999999(\\.99?)?)$", "error_msg": "Sale price should be a positive amount between $0.01 and $999,999.99"}]}, "trade_in_value": {"required": false, "type": "decimal", "validation_rules": [{"check": "trade_in_value_range", "isValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$", "error_msg": "Trade-in value should be a positive amount up to $999,999.99"}, {"check": "trade_in_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv7d"], "error_msg": "Trade-in value does not match between Bill of Sale and MV-7D"}]}, "tavt_tax_amount": {"required": true, "type": "decimal", "validation_rules": [{"check": "tavt_tax_amount_range", "isValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "error_msg": "TAVT tax amount is required and should be between $0 and $99,999.99"}, {"check": "tavt_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv1", "dealer_dmv"], "tolerance": 500.0, "error_msg": "TAVT tax amount differs by more than $500 across documents - requires manual review"}, {"check": "tavt_calculation_verification", "isValidationType": "CROSS_FIELD", "calculation_base": "sale_price", "county_dependent": true, "trade_in_deduction": true, "target_groups": ["bos", "mv1", "dealer_dmv"], "error_msg": "TAVT calculation does not match expected amount based on sale price and county tax rate"}]}, "total_amount_due": {"required": false, "type": "decimal", "validation_rules": [{"check": "total_amount_due_range", "isValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,5}(\\.\\d{1,2})?|999999(\\.99?)?)$", "error_msg": "Total amount due should be a positive amount up to $999,999.99"}, {"check": "total_amount_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv7d", "dealer_dmv"], "error_msg": "Total amount due does not match across funding and documents"}]}, "lien_holder_name": {"required": false, "type": "string", "validation_rules": [{"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Lien holder name should be 2-100 characters with valid business name characters"}, {"check": "lien_holder_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv1", "title"], "error_msg": "Lien holder name does not match across documents"}]}, "full_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Full name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}, {"check": "full_name_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv1"], "field_mappings": {"bos": "buyer_name", "mv1": "buyer_full_name"}, "error_msg": "Full name does not match across documents"}]}, "date_of_birth": {"required": true, "type": "date", "validation_rules": [{"check": "dob_format_check", "isValidationType": "REGEX", "regex": "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-9])$", "error_msg": "Date of birth is required and should be in MM/DD/YYYY format with a valid year between 1900 and 2009"}, {"check": "dob_age_16_to_120_check", "isValidationType": "REGEX", "regex": "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/((19[0-9]{2})|200[0-8])$", "error_msg": "Date of birth should be in MM/DD/YYYY format and indicate an age between 16 and 120 years"}]}, "address__street": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Street address is required"}, {"check": "address_format", "isValidationType": "REGEX", "regex": "^[0-9]+\\s+[A-Za-z0-9\\s,.-]{1,100}$", "error_msg": "Street address should start with a number followed by street name (1-100 characters)"}]}, "city": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "City is required"}, {"check": "city_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "City should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "state": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "State is required"}, {"check": "state_format", "isValidationType": "REGEX", "regex": "^[A-Z]{2}$", "error_msg": "State should be a 2-letter abbreviation (e.g., GA, FL)"}]}, "zip": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "ZIP code is required"}, {"check": "zip_format", "isValidationType": "REGEX", "regex": "^[0-9]{5}(-[0-9]{4})?$", "error_msg": "ZIP code should be 5 digits or 5+4 format (e.g., 12345 or 12345-6789)"}]}, "driver_s_license_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Driver's license number is required"}, {"check": "license_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{6,20}$", "error_msg": "Driver's license number should be 6-20 alphanumeric characters"}, {"check": "license_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv1", "mv7d"], "field_mappings": {"mv7d": "customer_id"}, "error_msg": "Driver's license number does not match across documents"}]}, "buyer_full_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Buyer full name is required"}, {"check": "name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "Buyer full name should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}, {"check": "full_name_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv1", "dl"], "field_mappings": {"bos": "buyer_name", "mv1": "buyer_full_name", "dl": "full_name"}, "error_msg": "Buyer full name does not match across documents"}]}, "county_of_residence": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "County of residence is required"}, {"check": "county_format", "isValidationType": "REGEX", "regex": "^[A-Za-z\\s'-]{2,50}$", "error_msg": "County should contain only letters, spaces, hyphens and apostrophes (2-50 characters)"}]}, "customer_id": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Customer ID (Driver's License Number) is required"}, {"check": "license_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{6,20}$", "error_msg": "Customer ID should be 6-20 alphanumeric characters"}, {"check": "customer_id_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["dl"], "field_mappings": {"dl": "driver_s_license_number"}, "error_msg": "Customer ID does not match across documents"}]}, "body_style": {"required": false, "type": "string", "validation_rules": [{"check": "body_style_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s-]{1,20}$", "error_msg": "Body style should contain only letters, numbers, spaces, and hyphens (1-20 characters)"}]}, "lien_holder_address": {"required": false, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Lien holder address is required"}]}, "dealer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Dealer name is required"}, {"check": "dealer_name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Dealer name should be 2-100 characters with valid business name characters"}]}, "dealer_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Dealer number is required"}, {"check": "dealer_number_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{3,20}$", "error_msg": "Dealer number should be 3-20 alphanumeric characters"}]}, "odometer_type": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Odometer type is required"}, {"check": "odometer_type_valid", "isValidationType": "REGEX", "regex": "^(Actual|Exceeds|Not Actual)$", "error_msg": "Odometer type should be 'Actual', 'Exceeds', or 'Not Actual'"}]}, "date_of_reassignment": {"required": true, "type": "date", "validation_rules": [{"check": "reassignment_date_format_and_year", "isValidationType": "REGEX", "regex": "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$", "error_msg": "Date of reassignment should be in MM/DD/YYYY format and between 2000 and 2025"}, {"check": "reassignment_date_required", "isValidationType": "REGEX", "regex": "^.+$", "error_msg": "Date of reassignment is required"}, {"check": "date_sequence", "isValidationType": "CROSS_FIELD", "date_fields": ["date_of_reassignment", "date_of_transfer"], "sequence_rule": "increasing", "error_msg": "Reassignment date must be before transfer date"}]}, "selling_dealer_name": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Selling dealer name is required"}, {"check": "dealer_name_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,100}$", "error_msg": "Selling dealer name should be 2-100 characters with valid business name characters"}, {"check": "selling_dealer_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["bos"], "error_msg": "Selling dealer name does not match across documents"}]}, "lien_satisfied": {"required": false, "type": "boolean", "validation_rules": [{"check": "boolean_format", "isValidationType": "EXPRESSION_TYPE", "expression": "value == True || value == False || value == \"true\" || value == \"false\" || value == 1 || value == 0", "error_msg": "Lien satisfied should be true or false"}]}, "date_of_transfer": {"required": true, "type": "date", "validation_rules": [{"check": "transfer_date_required", "isValidationType": "REGEX", "regex": "^.+$", "error_msg": "Date of transfer is required"}, {"check": "transfer_date_format_and_range", "isValidationType": "REGEX", "regex": "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(200\\d|201\\d|202[0-5])$", "error_msg": "Date of transfer should be in MM/DD/YYYY format and between 01/01/2000 and current date"}]}, "title_number": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Title number is required"}, {"check": "title_number_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9]{6,20}$", "error_msg": "Title number should be 6-20 alphanumeric characters"}]}, "dealer_fees": {"required": true, "type": "decimal", "validation_rules": [{"check": "dealer_fees_amount_range", "isValidationType": "REGEX", "regex": "^(0(\\.\\d{1,2})?|[1-9]\\d{0,4}(\\.\\d{1,2})?|99999(\\.99?)?)$", "error_msg": "Dealer fees should be a positive amount between $0 and $99,999.99"}, {"check": "dealer_fees_required", "isValidationType": "REGEX", "regex": "^.+$", "error_msg": "Dealer fees is required"}]}, "expiration_date": {"required": true, "type": "date", "validation_rules": [{"check": "dl_expiration_required", "isValidationType": "REGEX", "regex": "^.+$", "error_msg": "Driver's license expiration date is required"}, {"check": "dl_expiration_format_and_future_year", "isValidationType": "REGEX", "regex": "^(0[1-9]|1[0-2])\\/(0[1-9]|[12]\\d|3[01])\\/(202[5-9]|20[3-9]\\d|21\\d{2})$", "error_msg": "Expiration date should be in MM/DD/YYYY format and must not be expired"}]}, "lien_release_section": {"required": false, "type": "string", "validation_rules": [{"check": "lien_release_format", "isValidationType": "REGEX", "regex": "^[A-Za-z0-9\\s&.,'-]{2,200}$", "error_msg": "Lien release section should be 2-200 characters with valid text characters"}]}, "seller_signature": {"required": true, "type": "boolean", "validation_rules": [{"check": "not_empty", "isValidationType": "EXPRESSION_TYPE", "expression": "value != null && value != \"\" && value != false", "error_msg": "Seller signature is required"}, {"check": "signature_present", "isValidationType": "EXPRESSION_TYPE", "expression": "value == True || value == \"true\" || value == 1", "error_msg": "Seller signature must be present for legal certification of transfer"}]}, "signatures": {"required": true, "type": "object", "validation_rules": [{"check": "signatures_present", "isValidationType": "EXPRESSION_TYPE", "expression": "seller_signature.value != null && seller_signature.value != \"\" && buyer_signature.value != null && buyer_signature.value != \"\"", "error_msg": "Both seller and buyer signatures are required for MV-7D"}]}, "deal_status": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Deal status is required"}, {"check": "deal_finalized", "isValidationType": "REGEX", "regex": "^(funded|accounting|finalized)$", "error_msg": "Deal must be fully finalized (funded, in accounting, or finalized) before processing"}]}, "title_received": {"required": true, "type": "boolean", "validation_rules": [{"check": "title_available", "isValidationType": "BOOLEAN", "error_msg": "Title or MSO must be physically or digitally available before processing"}]}, "reassignment_needed": {"required": false, "type": "boolean", "validation_rules": [{"check": "reassignment_check", "isValidationType": "EXPRESSION_TYPE", "expression": "vehicle_type.value != \"used\" || title_fully_assigned.value != True || mv7d_included.value != None", "error_msg": "Used vehicle with fully assigned title requires MV-7D reassignment form"}]}, "date_consistency_check": {"required": false, "type": "object", "validation_rules": [{"check": "date_sequence", "isValidationType": "CROSS_FIELD", "date_fields": ["date_of_transfer", "date_of_reassignment"], "sequence_rule": "increasing", "error_msg": "Transfer dates must be in chronological order across documents"}]}, "county_tax_jurisdiction": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "County tax jurisdiction is required"}, {"check": "county_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["mv1", "dealer_dmv"], "field_mappings": {"mv1": "county_of_residence", "dealer_dmv": "county_tax_jurisdiction"}, "error_msg": "County tax jurisdiction must match across MV1 and Dealer DMV records"}]}, "mv34_required": {"required": false, "type": "boolean", "validation_rules": [{"check": "mv34_trigger", "isValidationType": "CONDITIONAL", "condition": "address_mismatch_detected == true", "error_msg": "MV-34 form required when addresses differ across documents"}]}, "title_chain_complete": {"required": true, "type": "boolean", "validation_rules": [{"check": "title_chain_validation", "isValidationType": "CROSS_FIELD", "target_groups": ["title", "mv7d", "bos"], "error_msg": "Title chain broken or not properly assigned between owner and dealership"}]}, "vehicle_type": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Vehicle type is required"}, {"check": "vehicle_type_valid", "isValidationType": "REGEX", "regex": "^(new|used)$", "error_msg": "Vehicle type must be 'new' or 'used'"}]}, "reassignment_form_complete": {"required": false, "type": "boolean", "validation_rules": [{"check": "reassignment_completeness", "isValidationType": "CONDITIONAL", "condition": "vehicle_type == 'used'", "required_fields": ["signatures", "vin", "odometer_reading", "date_of_reassignment"], "error_msg": "Reassignment form missing required signatures, VIN, mileage, or transfer date"}]}, "mv7d_inclusion_check": {"required": false, "type": "boolean", "validation_rules": [{"check": "mv7d_required_check", "isValidationType": "CONDITIONAL", "condition": "vehicle_type == 'used' && title_fully_assigned == true", "required_when_true": true, "error_msg": "Used vehicle with fully assigned title requires MV-7D reassignment form to be included"}]}, "title_availability_status": {"required": true, "type": "string", "validation_rules": [{"check": "not_empty", "isValidationType": "REGEX", "regex": "^.{1,}$", "error_msg": "Title availability status is required"}, {"check": "title_ready_for_processing", "isValidationType": "REGEX", "regex": "^(physically_available|digitally_available)$", "error_msg": "Title or MSO must be physically or digitally available before processing"}]}, "tax_calculation_verification": {"required": true, "type": "object", "validation_rules": [{"check": "tavt_calculation_accuracy", "isValidationType": "CROSS_FIELD", "target_groups": ["bos", "mv1", "dealer_dmv"], "calculation_fields": ["sale_price", "county_tax_jurisdiction", "trade_in_value"], "tolerance": 500.0, "error_msg": "TAVT calculation verification failed - tax amount differs by more than $500 across documents"}]}, "address_county_validation": {"required": true, "type": "object", "validation_rules": [{"check": "address_county_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["dl", "mv1", "bos", "dealer_dmv"], "field_mappings": {"dl": "address", "mv1": "buyer_address", "bos": "buyer_address", "dealer_dmv": "buyer_address"}, "county_impact": "tavt_calculation", "error_msg": "Address or county mismatch affects TAVT calculation - all addresses must match or MV-34 required"}]}, "document_legibility_check": {"required": true, "type": "boolean", "validation_rules": [{"check": "document_readable", "isValidationType": "BOOLEAN", "error_msg": "Document information is illegible and cannot be processed"}]}, "tax_jurisdiction_verification": {"required": true, "type": "object", "validation_rules": [{"check": "jurisdiction_consistency", "isValidationType": "CROSS_FIELD", "target_groups": ["dl", "mv1", "dealer_dmv"], "field_mappings": {"dl": "address", "mv1": "county_of_residence", "dealer_dmv": "county_tax_jurisdiction"}, "error_msg": "Tax jurisdiction must be consistent across driver's license address, MV1 county, and dealer DMV records"}]}, "sales_price_tax_base": {"required": true, "type": "decimal", "validation_rules": [{"check": "tax_base_calculation", "isValidationType": "CROSS_FIELD", "calculation_formula": "sale_price - trade_in_value", "target_groups": ["bos", "mv1"], "error_msg": "Sales price used for tax calculation must be consistent across BOS and MV1"}]}, "title_fully_assigned": {"required": false, "type": "boolean", "validation_rules": [{"check": "title_assignment_status", "isValidationType": "BOOLEAN", "error_msg": "Title assignment status must be determined for reassignment requirements"}]}, "dealer_dmv_submission_ready": {"required": true, "type": "boolean", "validation_rules": [{"check": "submission_readiness", "isValidationType": "EXPRESSION_TYPE", "expression": "deal_finalized.value == True && title_available.value == True && all_documents_complete.value == True", "error_msg": "All conditions must be met before Dealer DMV submission"}]}}}}