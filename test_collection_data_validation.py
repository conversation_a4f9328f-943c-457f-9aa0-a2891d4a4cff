#!/usr/bin/env python3
"""
Test script for the updated collection_data.json with direct expressions
"""

import json
from validation_utils import ValidationUtils

def load_collection_data():
    """Load the collection_data.json file"""
    with open('collection_data.json', 'r') as f:
        return json.load(f)

def test_vin_consistency():
    """Test VIN consistency validation"""
    print("=== Testing VIN Consistency ===")
    
    validator = ValidationUtils()
    collection_data = load_collection_data()
    
    # Get VIN field configuration
    vin_config = collection_data["properties"]["fields"]["vin"]
    
    # Mock data with matching VINs
    all_data = {
        "groups": {
            "bos": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
            "mv1": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
            "mv7d": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}},
            "title": {"fields": {"vin": {"value": "1HGBH41JXMN109186"}}}
        }
    }
    
    errors = validator.validate_field("1HGBH41JXMN109186", vin_config, {}, all_data, "bos", "vin")
    print(f"Matching VINs: {errors}")
    
    # Mock data with non-matching VINs
    all_data["groups"]["title"]["fields"]["vin"]["value"] = "1HGBH41JXMN109187"
    
    errors = validator.validate_field("1HGBH41JXMN109186", vin_config, {}, all_data, "bos", "vin")
    print(f"Non-matching VINs: {errors}")
    
    print()

def test_buyer_name_consistency():
    """Test buyer name consistency with multiple expressions"""
    print("=== Testing Buyer Name Consistency ===")
    
    validator = ValidationUtils()
    collection_data = load_collection_data()
    
    # Get buyer_name field configuration
    buyer_name_config = collection_data["properties"]["fields"]["buyer_name"]
    
    # Mock data with all required fields present and matching
    all_data = {
        "groups": {
            "bos": {"fields": {"buyer_name": {"value": "John Doe"}}},
            "dl": {"fields": {"full_name": {"value": "John Doe"}}},
            "mv1": {"fields": {"buyer_full_name": {"value": "John Doe"}}},
            "mv7d": {"fields": {"buyer_name": {"value": "John Doe"}}},
            "title": {"fields": {"buyer_name": {"value": "John Doe"}}}
        }
    }
    
    errors = validator.validate_field("John Doe", buyer_name_config, {}, all_data, "bos", "buyer_name")
    print(f"All names present and matching: {errors}")
    
    # Test with missing DL full name
    all_data["groups"]["dl"]["fields"]["full_name"]["value"] = ""
    
    errors = validator.validate_field("John Doe", buyer_name_config, {}, all_data, "bos", "buyer_name")
    print(f"Missing DL full name: {errors}")
    
    # Test with non-matching names
    all_data["groups"]["dl"]["fields"]["full_name"]["value"] = "John Doe"
    all_data["groups"]["mv1"]["fields"]["buyer_full_name"]["value"] = "Jane Smith"
    
    errors = validator.validate_field("John Doe", buyer_name_config, {}, all_data, "bos", "buyer_name")
    print(f"Non-matching names: {errors}")
    
    print()

def test_odometer_consistency():
    """Test odometer consistency with tolerance"""
    print("=== Testing Odometer Consistency ===")
    
    validator = ValidationUtils()
    collection_data = load_collection_data()
    
    # Get odometer_reading field configuration
    odometer_config = collection_data["properties"]["fields"]["odometer_reading"]
    
    # Mock data within tolerance (10 units)
    all_data = {
        "groups": {
            "bos": {"fields": {"odometer_reading": {"value": 50000}}},
            "mv1": {"fields": {"odometer_reading": {"value": 50005}}},
            "mv7d": {"fields": {"odometer_reading": {"value": 50008}}},
            "title": {"fields": {"odometer_reading": {"value": 50003}}}
        }
    }
    
    errors = validator.validate_field(50000, odometer_config, {}, all_data, "bos", "odometer_reading")
    print(f"Within tolerance: {errors}")
    
    # Mock data outside tolerance
    all_data["groups"]["title"]["fields"]["odometer_reading"]["value"] = 50015
    
    errors = validator.validate_field(50000, odometer_config, {}, all_data, "bos", "odometer_reading")
    print(f"Outside tolerance: {errors}")
    
    print()

def test_boolean_validation():
    """Test boolean validation"""
    print("=== Testing Boolean Validation ===")
    
    validator = ValidationUtils()
    collection_data = load_collection_data()
    
    # Get lien_satisfied field configuration
    lien_config = collection_data["properties"]["fields"]["lien_satisfied"]
    
    # Test valid boolean values
    for value in [True, False, "true", "false", 1, 0]:
        errors = validator.validate_field(value, lien_config, {})
        print(f"Boolean value '{value}': {errors}")
    
    # Test invalid boolean value
    errors = validator.validate_field("maybe", lien_config, {})
    print(f"Invalid boolean 'maybe': {errors}")
    
    print()

def test_signature_validation():
    """Test signature validation"""
    print("=== Testing Signature Validation ===")
    
    validator = ValidationUtils()
    collection_data = load_collection_data()
    
    # Get signatures field configuration
    signatures_config = collection_data["properties"]["fields"]["signatures"]
    
    # Mock data with both signatures present
    all_data = {
        "groups": {
            "current": {
                "fields": {
                    "seller_signature": {"value": "John Seller"},
                    "buyer_signature": {"value": "Jane Buyer"}
                }
            }
        }
    }
    
    errors = validator.validate_field({}, signatures_config, {}, all_data, "current", "signatures")
    print(f"Both signatures present: {errors}")
    
    # Mock data with missing buyer signature
    all_data["groups"]["current"]["fields"]["buyer_signature"]["value"] = ""
    
    errors = validator.validate_field({}, signatures_config, {}, all_data, "current", "signatures")
    print(f"Missing buyer signature: {errors}")
    
    print()

def test_conditional_validation():
    """Test conditional validation"""
    print("=== Testing Conditional Validation ===")
    
    validator = ValidationUtils()
    collection_data = load_collection_data()
    
    # Get reassignment_needed field configuration
    reassignment_config = collection_data["properties"]["fields"]["reassignment_needed"]
    
    # Mock data where MV7D is not required (new vehicle)
    all_data = {
        "groups": {
            "current": {
                "fields": {
                    "vehicle_type": {"value": "new"},
                    "title_fully_assigned": {"value": True},
                    "mv7d_included": {"value": None}
                }
            }
        }
    }
    
    errors = validator.validate_field(False, reassignment_config, {}, all_data, "current", "reassignment_needed")
    print(f"New vehicle (no MV7D required): {errors}")
    
    # Mock data where MV7D is required but missing
    all_data["groups"]["current"]["fields"]["vehicle_type"]["value"] = "used"
    
    errors = validator.validate_field(True, reassignment_config, {}, all_data, "current", "reassignment_needed")
    print(f"Used vehicle with missing MV7D: {errors}")
    
    # Mock data where MV7D is required and present
    all_data["groups"]["current"]["fields"]["mv7d_included"]["value"] = True
    
    errors = validator.validate_field(True, reassignment_config, {}, all_data, "current", "reassignment_needed")
    print(f"Used vehicle with MV7D present: {errors}")
    
    print()

def main():
    """Run all tests"""
    print("Testing Collection Data Validation with Direct Expressions")
    print("=" * 65)
    
    try:
        test_vin_consistency()
        test_buyer_name_consistency()
        test_odometer_consistency()
        test_boolean_validation()
        test_signature_validation()
        test_conditional_validation()
        
        print("All tests completed successfully!")
        
    except Exception as e:
        print(f"Error during testing: {str(e)}")

if __name__ == "__main__":
    main()
