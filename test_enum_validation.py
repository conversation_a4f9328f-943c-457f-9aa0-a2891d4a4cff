#!/usr/bin/env python3
"""
Test script for the new enum-based validation system with 4 validation types
"""

import json
from validation_utils import ValidationUtils, ValidationType

def test_validation_types_enum():
    """Test that all validation types are properly defined in enum"""
    print("=== Testing Validation Types Enum ===")
    
    expected_types = ["REGEX", "REGEX_LIST", "EXPRESSION_TYPE", "EXPRESSION_TYPE_LIST"]
    actual_types = [vt.value for vt in ValidationType]
    
    print(f"Expected types: {expected_types}")
    print(f"Actual types: {actual_types}")
    print(f"All types present: {set(expected_types) == set(actual_types)}")
    print()

def test_regex_validation():
    """Test single REGEX validation"""
    print("=== Testing REGEX Validation ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "alphanumeric",
                "isValidationType": "REGEX",
                "regex": "^[A-Za-z0-9]+$",
                "error_msg": "Value should be alphanumeric"
            }
        ]
    }
    
    # Valid case
    errors = validator.validate_field("ABC123", field_config, {})
    print(f"Valid alphanumeric 'ABC123': {errors}")
    
    # Invalid case
    errors = validator.validate_field("ABC-123", field_config, {})
    print(f"Invalid alphanumeric 'ABC-123': {errors}")
    
    print()

def test_regex_list_validation():
    """Test REGEX_LIST validation"""
    print("=== Testing REGEX_LIST Validation ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "multiple_regex_patterns",
                "isValidationType": "REGEX_LIST",
                "regexes": ["^.{1,}$", "^[A-Za-z0-9]+$"],
                "error_msgs": ["Value is required", "Value should be alphanumeric"],
                "conditionType": "AND"
            }
        ]
    }
    
    # Valid case
    errors = validator.validate_field("ABC123", field_config, {})
    print(f"Valid value 'ABC123': {errors}")
    
    # Empty value (fails first regex)
    errors = validator.validate_field("", field_config, {})
    print(f"Empty value: {errors}")
    
    # Invalid characters (fails second regex)
    errors = validator.validate_field("ABC-123", field_config, {})
    print(f"Invalid characters 'ABC-123': {errors}")
    
    print()

def test_expression_type_validation():
    """Test single EXPRESSION_TYPE validation"""
    print("=== Testing EXPRESSION_TYPE Validation ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "cross_field_match",
                "isValidationType": "EXPRESSION_TYPE",
                "expression": "bos.buyer_name.value == dl.full_name.value",
                "error_msg": "Buyer names must match"
            }
        ]
    }
    
    # Mock data with matching names
    all_data = {
        "groups": {
            "bos": {"fields": {"buyer_name": {"value": "John Doe"}}},
            "dl": {"fields": {"full_name": {"value": "John Doe"}}}
        }
    }
    
    errors = validator.validate_field("John Doe", field_config, {}, all_data, "bos", "buyer_name")
    print(f"Matching names: {errors}")
    
    # Mock data with non-matching names
    all_data["groups"]["dl"]["fields"]["full_name"]["value"] = "Jane Smith"
    
    errors = validator.validate_field("John Doe", field_config, {}, all_data, "bos", "buyer_name")
    print(f"Non-matching names: {errors}")
    
    print()

def test_expression_type_list_validation():
    """Test EXPRESSION_TYPE_LIST validation"""
    print("=== Testing EXPRESSION_TYPE_LIST Validation ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "buyer_name_consistency",
                "isValidationType": "EXPRESSION_TYPE_LIST",
                "expressions": [
                    "bos.buyer_name.value != None && bos.buyer_name.value != \"\"",
                    "dl.full_name.value != None && dl.full_name.value != \"\"",
                    "bos.buyer_name.value == dl.full_name.value"
                ],
                "error_msgs": [
                    "BOS buyer name is required",
                    "DL full name is required",
                    "Buyer names must match"
                ],
                "conditionType": "AND"
            }
        ]
    }
    
    # Mock data with all conditions met
    all_data = {
        "groups": {
            "bos": {"fields": {"buyer_name": {"value": "John Doe"}}},
            "dl": {"fields": {"full_name": {"value": "John Doe"}}}
        }
    }
    
    errors = validator.validate_field("John Doe", field_config, {}, all_data, "bos", "buyer_name")
    print(f"All conditions met: {errors}")
    
    # Mock data with missing DL name
    all_data["groups"]["dl"]["fields"]["full_name"]["value"] = ""
    
    errors = validator.validate_field("John Doe", field_config, {}, all_data, "bos", "buyer_name")
    print(f"Missing DL name: {errors}")
    
    # Mock data with non-matching names
    all_data["groups"]["dl"]["fields"]["full_name"]["value"] = "Jane Smith"
    
    errors = validator.validate_field("John Doe", field_config, {}, all_data, "bos", "buyer_name")
    print(f"Non-matching names: {errors}")
    
    print()

def test_invalid_validation_type():
    """Test handling of invalid validation types"""
    print("=== Testing Invalid Validation Type ===")
    
    validator = ValidationUtils()
    
    field_config = {
        "required": True,
        "validation_rules": [
            {
                "check": "invalid_type",
                "isValidationType": "INVALID_TYPE",
                "error_msg": "This should fail"
            }
        ]
    }
    
    errors = validator.validate_field("test", field_config, {})
    print(f"Invalid validation type: {errors}")
    
    print()

def test_collection_data_compatibility():
    """Test that updated collection_data.json works with new system"""
    print("=== Testing Collection Data Compatibility ===")
    
    try:
        with open('collection_data.json', 'r') as f:
            collection_data = json.load(f)
        
        validator = ValidationUtils()
        
        # Test deal_number field with REGEX_LIST
        deal_number_config = collection_data["properties"]["fields"]["deal_number"]
        
        errors = validator.validate_field("ABC123", deal_number_config, {})
        print(f"Deal number validation: {errors}")
        
        # Test buyer_name field with EXPRESSION_TYPE_LIST
        buyer_name_config = collection_data["properties"]["fields"]["buyer_name"]
        
        # Mock data for buyer name validation
        all_data = {
            "groups": {
                "bos": {"fields": {"buyer_name": {"value": "John Doe"}}},
                "dl": {"fields": {"full_name": {"value": "John Doe"}}},
                "mv1": {"fields": {"buyer_full_name": {"value": "John Doe"}}},
                "mv7d": {"fields": {"buyer_name": {"value": "John Doe"}}},
                "title": {"fields": {"buyer_name": {"value": "John Doe"}}}
            }
        }
        
        errors = validator.validate_field("John Doe", buyer_name_config, {}, all_data, "bos", "buyer_name")
        print(f"Buyer name validation: {errors}")
        
        print("Collection data compatibility: SUCCESS")
        
    except Exception as e:
        print(f"Collection data compatibility: FAILED - {str(e)}")
    
    print()

def main():
    """Run all tests"""
    print("Testing Enum-Based Validation System")
    print("=" * 50)
    
    test_validation_types_enum()
    test_regex_validation()
    test_regex_list_validation()
    test_expression_type_validation()
    test_expression_type_list_validation()
    test_invalid_validation_type()
    test_collection_data_compatibility()
    
    print("All tests completed!")

if __name__ == "__main__":
    main()
